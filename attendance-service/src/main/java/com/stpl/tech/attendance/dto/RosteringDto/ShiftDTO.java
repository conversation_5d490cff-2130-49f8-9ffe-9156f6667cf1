package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShiftDTO {
    private Integer shiftId;
    private String shiftName;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private List<Integer> unitIds;
    private List<Integer> employeeIds; 
}
