package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShiftResponseDTO {
    
    private Integer shiftId; // Auto-generated from DB
    private String shiftName;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private String status;
    private String createdBy; // Set from HttpServletRequest (employee ID)
    private LocalDateTime creationTime; // Set from current system time
    private String updatedBy; // Set from HttpServletRequest (employee ID)
    private LocalDateTime updationTime; // Set from current system time
}
