package com.stpl.tech.attendance.repository.RosteringRepository;

import com.stpl.tech.attendance.entity.RosteringEntity.EmpShiftMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface EmpShiftMappingRepository extends JpaRepository<EmpShiftMapping, Integer> {
    
    /**
     * Find all mappings by employee ID
     */
    List<EmpShiftMapping> findByEmpIdAndStatus(Integer empId, String status);
    
    /**
     * Find all mappings by shift ID
     */
    List<EmpShiftMapping> findByShiftIdAndStatus(Integer shiftId, String status);
    
    /**
     * Find employee shift mappings within date range
     */
    @Query("SELECT esm FROM EmpShiftMapping esm WHERE esm.empId = :empId AND esm.status = :status " +
           "AND ((:startDate IS NULL OR esm.endDate IS NULL OR esm.endDate >= :startDate) " +
           "AND (:endDate IS NULL OR esm.startDate IS NULL OR esm.startDate <= :endDate))")
    List<EmpShiftMapping> findByEmpIdAndStatusAndDateRange(
        @Param("empId") Integer empId, 
        @Param("status") String status,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate
    );
    
    /**
     * Find current active shift for employee
     */
    @Query("SELECT esm FROM EmpShiftMapping esm WHERE esm.empId = :empId AND esm.status = :status " +
           "AND (esm.startDate IS NULL OR esm.startDate <= :currentDate) " +
           "AND (esm.endDate IS NULL OR esm.endDate >= :currentDate)")
    Optional<EmpShiftMapping> findCurrentShiftByEmpId(
        @Param("empId") Integer empId, 
        @Param("status") String status,
        @Param("currentDate") LocalDateTime currentDate
    );
    
    /**
     * Find all employees for a specific shift
     */
    @Query("SELECT esm.empId FROM EmpShiftMapping esm WHERE esm.shiftId = :shiftId AND esm.status = :status")
    List<Integer> findEmpIdsByShiftIdAndStatus(@Param("shiftId") Integer shiftId, @Param("status") String status);
    
    /**
     * Find employee shift mappings with shift details
     */
    @Query("SELECT esm FROM EmpShiftMapping esm JOIN FETCH esm.shift s WHERE esm.empId = :empId AND esm.status = :status")
    List<EmpShiftMapping> findByEmpIdAndStatusWithShift(@Param("empId") Integer empId, @Param("status") String status);
    
    /**
     * Find overlapping shift mappings for employee
     */
    @Query("SELECT esm FROM EmpShiftMapping esm WHERE esm.empId = :empId AND esm.status = :status " +
           "AND esm.id != :excludeId " +
           "AND ((:startDate IS NULL OR esm.endDate IS NULL OR esm.endDate >= :startDate) " +
           "AND (:endDate IS NULL OR esm.startDate IS NULL OR esm.startDate <= :endDate))")
    List<EmpShiftMapping> findOverlappingMappings(
        @Param("empId") Integer empId,
        @Param("status") String status,
        @Param("excludeId") Integer excludeId,
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );
    
    /**
     * Check if employee has active shift mapping
     */
    boolean existsByEmpIdAndStatus(Integer empId, String status);
}
