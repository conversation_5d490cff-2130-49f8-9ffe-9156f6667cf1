package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CafeLiveDashboardResponseDTO {
    private DashboardViewDTO dashboardView;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DashboardViewDTO {
        private CafeDashboardViewDTO cafeDashboardView;
        private DateRangeDTO date;
        private CafeDashboardStatsDTO cafeDashboard;
        private java.util.List<ShiftInfoDTO> shifts;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CafeDashboardViewDTO {
        private boolean cafeDashboard;
        private boolean shiftDashboard;
        private boolean employeeDashboard;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DateRangeDTO {
        private String startDate; // Format: "2024-01-01"
        private String endDate;   // Format: "2024-01-31"
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CafeDashboardStatsDTO {
        private Integer actual;
        private Integer ideal;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShiftInfoDTO {
        private String shiftId;
        private String startDate;  // Format: "2024-01-01T09:00:00Z"
        private String endDate;    // Format: "2024-01-01T17:00:00Z"
        private Integer numberOfEmployees;
        private String shiftName;
    }
}
