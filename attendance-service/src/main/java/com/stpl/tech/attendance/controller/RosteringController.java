package com.stpl.tech.attendance.controller;

import com.stpl.tech.attendance.constants.ApiConstants;
import com.stpl.tech.attendance.dto.RosteringDto.*;
import com.stpl.tech.attendance.service.RosteringService.RosteringService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping(ApiConstants.Paths.ROSTER)
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Rostering", description = "Rostering management APIs")
public class RosteringController {

    private final RosteringService rosteringService;

    @GetMapping("/dashboard/cafe-live-dashboard")
    @Operation(summary = "Get cafe live dashboard", description = "Get live dashboard data for cafes/units")
    public ResponseEntity<List<CafeLiveDashboardDTO>> getCafeLiveDashboard(
            @Parameter(description = "Unit ID filter (optional)")
            @RequestParam(required = false) Integer unitId) {
        log.info("Getting cafe live dashboard for unitId: {}", unitId);
        List<CafeLiveDashboardDTO> dashboard = rosteringService.getCafeLiveDashboard(unitId);
        return ResponseEntity.ok(dashboard);
    }

    @GetMapping("/shifts/shift-employees")
    @Operation(summary = "Get shift employees", description = "Get employees assigned to shifts")
    public ResponseEntity<Page<ShiftEmployeesDTO>> getShiftEmployees(
            @Parameter(description = "Shift ID filter (optional)")
            @RequestParam(required = false) Integer shiftId,
            @Parameter(description = "Unit ID filter (optional)")
            @RequestParam(required = false) Integer unitId,
            Pageable pageable) {
        log.info("Getting shift employees for shiftId: {}, unitId: {}", shiftId, unitId);
        Page<ShiftEmployeesDTO> employees = rosteringService.getShiftEmployees(shiftId, unitId, pageable);
        return ResponseEntity.ok(employees);
    }

    @GetMapping("/shifts/emp-shift-data/{empId}")
    @Operation(summary = "Get employee shift data", description = "Get shift assignments for a specific employee")
    public ResponseEntity<List<EmpShiftMappingDTO>> getEmpShiftData(
            @Parameter(description = "Employee ID", required = true)
            @PathVariable Integer empId,
            @Parameter(description = "Start date filter (optional)")
            @RequestParam(required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "End date filter (optional)")
            @RequestParam(required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        log.info("Getting employee shift data for empId: {}, startDate: {}, endDate: {}", empId, startDate, endDate);
        List<EmpShiftMappingDTO> shiftData = rosteringService.getEmpShiftData(empId, startDate, endDate);
        return ResponseEntity.ok(shiftData);
    }

    @PutMapping("/shifts/emp-shift-update")
    @Operation(summary = "Update employee shift", description = "Update or create employee shift assignment")
    public ResponseEntity<EmpShiftMappingDTO> updateEmpShift(
            @Parameter(description = "Employee shift update request", required = true)
            @Valid @RequestBody EmpShiftUpdateRequestDTO request) {
        log.info("Updating employee shift: {}", request);
        EmpShiftMappingDTO updated = rosteringService.updateEmpShift(request);
        return ResponseEntity.ok(updated);
    }

    @GetMapping("/shifts/cafe-shift-data")
    @Operation(summary = "Get cafe shift data", description = "Get shift data for cafes/units")
    public ResponseEntity<List<CafeShiftDataDTO>> getCafeShiftData(
            @Parameter(description = "Unit ID filter (optional)")
            @RequestParam(required = false) Integer unitId) {
        log.info("Getting cafe shift data for unitId: {}", unitId);
        List<CafeShiftDataDTO> shiftData = rosteringService.getCafeShiftData(unitId);
        return ResponseEntity.ok(shiftData);
    }

    @GetMapping("/employees/get-hierarchy-employees")
    @Operation(summary = "Get hierarchy employees", description = "Get employee hierarchy data")
    public ResponseEntity<List<HierarchyEmployeeDTO>> getHierarchyEmployees(
            @Parameter(description = "Manager ID to get subordinates (optional)")
            @RequestParam(required = false) Integer managerId,
            @Parameter(description = "Include all subordinates recursively")
            @RequestParam(defaultValue = "false") boolean includeSubordinates) {
        log.info("Getting hierarchy employees for managerId: {}, includeSubordinates: {}", managerId, includeSubordinates);
        List<HierarchyEmployeeDTO> employees = rosteringService.getHierarchyEmployees(managerId, includeSubordinates);
        return ResponseEntity.ok(employees);
    }

    // Additional CRUD endpoints for shifts

    @PostMapping("/shifts")
    @Operation(summary = "Create shift", description = "Create a new shift")
    public ResponseEntity<ShiftDTO> createShift(
            @Parameter(description = "Shift data", required = true)
            @Valid @RequestBody ShiftDTO shiftDTO) {
        log.info("Creating new shift: {}", shiftDTO.getShiftName());
        ShiftDTO created = rosteringService.createShift(shiftDTO);
        return ResponseEntity.ok(created);
    }

    @PutMapping("/shifts/{shiftId}")
    @Operation(summary = "Update shift", description = "Update an existing shift")
    public ResponseEntity<ShiftDTO> updateShift(
            @Parameter(description = "Shift ID", required = true)
            @PathVariable Integer shiftId,
            @Parameter(description = "Updated shift data", required = true)
            @Valid @RequestBody ShiftDTO shiftDTO) {
        log.info("Updating shift with ID: {}", shiftId);
        ShiftDTO updated = rosteringService.updateShift(shiftId, shiftDTO);
        return ResponseEntity.ok(updated);
    }


    @GetMapping("/shifts")
    @Operation(summary = "Get all shifts", description = "Get all shifts with optional status filter")
    public ResponseEntity<List<ShiftDTO>> getAllShifts(
            @Parameter(description = "Status filter (optional)")
            @RequestParam(required = false) String status) {
        log.info("Getting all shifts with status: {}", status);
        List<ShiftDTO> shifts = rosteringService.getAllShifts(status);
        return ResponseEntity.ok(shifts);
    }

    // Shift-Cafe mapping endpoints

    @PostMapping("/shifts/{shiftId}/units/{unitId}")
    @Operation(summary = "Create shift-cafe mapping", description = "Map a shift to a cafe/unit")
    public ResponseEntity<ShiftCafeMappingDTO> createShiftCafeMapping(
            @Parameter(description = "Shift ID", required = true)
            @PathVariable Integer shiftId,
            @Parameter(description = "Unit ID", required = true)
            @PathVariable Integer unitId,
            @Parameter(description = "Created by user")
            @RequestParam String createdBy) {
        log.info("Creating shift cafe mapping for shiftId: {}, unitId: {}", shiftId, unitId);
        ShiftCafeMappingDTO mapping = rosteringService.createShiftCafeMapping(shiftId, unitId, createdBy);
        return ResponseEntity.ok(mapping);
    }

    @DeleteMapping("/shifts/{shiftId}/units/{unitId}")
    @Operation(summary = "Delete shift-cafe mapping", description = "Remove mapping between shift and cafe/unit")
    public ResponseEntity<Void> deleteShiftCafeMapping(
            @Parameter(description = "Shift ID", required = true)
            @PathVariable Integer shiftId,
            @Parameter(description = "Unit ID", required = true)
            @PathVariable Integer unitId) {
        log.info("Deleting shift cafe mapping for shiftId: {}, unitId: {}", shiftId, unitId);
        rosteringService.deleteShiftCafeMapping(shiftId, unitId);
        return ResponseEntity.ok().build();
    }

    // Employee-Shift mapping endpoints

    @PostMapping("/employees/shift-mapping")
    @Operation(summary = "Create employee shift mapping", description = "Assign an employee to a shift")
    public ResponseEntity<EmpShiftMappingDTO> createEmpShiftMapping(
            @Parameter(description = "Employee shift mapping request", required = true)
            @Valid @RequestBody EmpShiftUpdateRequestDTO request) {
        log.info("Creating employee shift mapping: {}", request);
        EmpShiftMappingDTO mapping = rosteringService.createEmpShiftMapping(request);
        return ResponseEntity.ok(mapping);
    }

    @DeleteMapping("/employees/shift-mapping/{mappingId}")
    @Operation(summary = "Delete employee shift mapping", description = "Remove employee from shift assignment")
    public ResponseEntity<Void> deleteEmpShiftMapping(
            @Parameter(description = "Mapping ID", required = true)
            @PathVariable Integer mappingId) {
        log.info("Deleting employee shift mapping with ID: {}", mappingId);
        rosteringService.deleteEmpShiftMapping(mappingId);
        return ResponseEntity.ok().build();
    }
}
