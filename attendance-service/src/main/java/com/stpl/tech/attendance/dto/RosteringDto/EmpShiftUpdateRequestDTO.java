package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmpShiftUpdateRequestDTO {
    
    private Integer id; // For updates, null for new mappings
    
    @NotNull(message = "Employee ID is required")
    private Integer empId;
    
    @NotNull(message = "Shift ID is required")
    private Integer shiftId;
    
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private LocalDateTime expectedStartDate;
    private LocalDateTime expectedEndDate;
    private LocalDateTime processingFrom;
    private LocalDateTime processingTo;
    private LocalDateTime businessFrom;
    private LocalDateTime businessTo;
    
    private String status = "ACTIVE";
    private String updatedBy;
}
