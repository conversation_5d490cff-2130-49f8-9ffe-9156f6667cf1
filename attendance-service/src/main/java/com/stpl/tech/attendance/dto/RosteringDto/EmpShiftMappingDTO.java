package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmpShiftMappingDTO {
    private Integer id;
    private Integer shiftId;
    private String shiftName;
    private Integer empId;
    private String empName;
    private String empCode;
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private LocalDateTime expectedStartDate;
    private LocalDateTime expectedEndDate;
    private LocalDateTime processingFrom;
    private LocalDateTime processingTo;
    private LocalDateTime businessFrom;
    private LocalDateTime businessTo;
    private String status;
    private String createdBy;
    private LocalDateTime creationTime;
    private String updatedBy;
    private LocalDateTime updationTime;
}
