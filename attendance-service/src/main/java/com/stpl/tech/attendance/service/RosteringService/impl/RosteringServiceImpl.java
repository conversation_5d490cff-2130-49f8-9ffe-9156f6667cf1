package com.stpl.tech.attendance.service.RosteringService.impl;

import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.dto.RosteringDto.*;
import com.stpl.tech.attendance.entity.RosteringEntity.EmpShiftMapping;
import com.stpl.tech.attendance.entity.RosteringEntity.ShiftCafeMapping;
import com.stpl.tech.attendance.repository.RosteringRepository.EmpShiftMappingRepository;
import com.stpl.tech.attendance.repository.RosteringRepository.ShiftCafeMappingRepository;
import com.stpl.tech.attendance.repository.RosteringRepository.ShiftRepository;
import com.stpl.tech.attendance.service.RosteringService.RosteringService;
import com.stpl.tech.attendance.entity.RosteringEntity.Shift;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class RosteringServiceImpl implements RosteringService {

    @Autowired
    private Shift shift;

    @Autowired
    private final ShiftRepository shiftRepository;

    @Autowired
    private final ShiftCafeMappingRepository shiftCafeMappingRepository;
    @Autowired
    private final EmpShiftMappingRepository empShiftMappingRepository;
    @Autowired
    private final UserCacheService userCacheService;
    @Autowired
    private final UnitCacheService unitCacheService;

    @Override
    @Transactional(readOnly = true)
    public List<CafeLiveDashboardDTO> getCafeLiveDashboard(Integer unitId) {
        log.info("Getting cafe live dashboard data for unitId: {}", unitId);

        List<CafeLiveDashboardDTO> dashboardData = new ArrayList<>();

        try {
            List<UnitBasicDetail> units;
            if (unitId != null) {
                UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(unitId);
                units = unit != null ? List.of(unit) : new ArrayList<>();
            } else {
                units = unitCacheService.getAllUnitBasicDetail();
            }

            for (UnitBasicDetail unit : units) {
                CafeLiveDashboardDTO dashboard = buildCafeDashboard(unit);
                dashboardData.add(dashboard);
            }

        } catch (Exception e) {
            log.error("Error getting cafe live dashboard data", e);
            throw new RuntimeException("Failed to get cafe live dashboard data", e);
        }

        return dashboardData;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ShiftEmployeesDTO> getShiftEmployees(Integer shiftId, Integer unitId, Pageable pageable) {
        log.info("Getting shift employees for shiftId: {}, unitId: {}", shiftId, unitId);

        try {
            List<ShiftEmployeesDTO> shiftEmployeesList = new ArrayList<>();

            List<com.stpl.tech.attendance.entity.RosteringEntity.Shift> shifts;
            if (shiftId != null) {
                shifts = shiftRepository.findById(shiftId)
                    .map(List::of)
                    .orElse(new ArrayList<>());
            } else if (unitId != null) {
                List<Integer> shiftIds = shiftCafeMappingRepository.findShiftIdsByUnitIdAndStatus(unitId, "ACTIVE");
                shifts = shiftRepository.findAllById(shiftIds);
            } else {
                shifts = shiftRepository.findByStatus("ACTIVE");
            }

            for (com.stpl.tech.attendance.entity.RosteringEntity.Shift shift : shifts) {
                ShiftEmployeesDTO shiftEmployees = buildShiftEmployees(shift);
                shiftEmployeesList.add(shiftEmployees);
            }

            // Apply pagination
            int start = (int) pageable.getOffset();
            int end = Math.min((start + pageable.getPageSize()), shiftEmployeesList.size());
            List<ShiftEmployeesDTO> pageContent = shiftEmployeesList.subList(start, end);

            return new PageImpl<>(pageContent, pageable, shiftEmployeesList.size());

        } catch (Exception e) {
            log.error("Error getting shift employees", e);
            throw new RuntimeException("Failed to get shift employees", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<EmpShiftMappingDTO> getEmpShiftData(Integer empId, LocalDateTime startDate, LocalDateTime endDate) {
        log.info("Getting employee shift data for empId: {}, startDate: {}, endDate: {}", empId, startDate, endDate);

        try {
            List<EmpShiftMapping> mappings = empShiftMappingRepository.findByEmpIdAndStatusAndDateRange(
                empId, "ACTIVE", startDate, endDate);

            return mappings.stream()
                .map(this::convertToEmpShiftMappingDTO)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error getting employee shift data for empId: {}", empId, e);
            throw new RuntimeException("Failed to get employee shift data", e);
        }
    }

    @Override
    public EmpShiftMappingDTO updateEmpShift(EmpShiftUpdateRequestDTO request) {
        log.info("Updating employee shift mapping: {}", request);

        try {
            EmpShiftMapping mapping;

            if (request.getId() != null) {
                // Update existing mapping
                mapping = empShiftMappingRepository.findById(request.getId())
                    .orElseThrow(() -> new RuntimeException("Employee shift mapping not found"));

                mapping.setShiftId(request.getShiftId());
                mapping.setStartDate(request.getStartDate());
                mapping.setEndDate(request.getEndDate());
                mapping.setExpectedStartDate(request.getExpectedStartDate());
                mapping.setExpectedEndDate(request.getExpectedEndDate());
                mapping.setProcessingFrom(request.getProcessingFrom());
                mapping.setProcessingTo(request.getProcessingTo());
                mapping.setBusinessFrom(request.getBusinessFrom());
                mapping.setBusinessTo(request.getBusinessTo());
                mapping.setStatus(request.getStatus());
                mapping.setUpdatedBy(request.getUpdatedBy());
            } else {
                // Create new mapping
                mapping = EmpShiftMapping.builder()
                    .empId(request.getEmpId())
                    .shiftId(request.getShiftId())
                    .startDate(request.getStartDate())
                    .endDate(request.getEndDate())
                    .expectedStartDate(request.getExpectedStartDate())
                    .expectedEndDate(request.getExpectedEndDate())
                    .processingFrom(request.getProcessingFrom())
                    .processingTo(request.getProcessingTo())
                    .businessFrom(request.getBusinessFrom())
                    .businessTo(request.getBusinessTo())
                    .status(request.getStatus())
                    .createdBy(request.getUpdatedBy())
                    .updatedBy(request.getUpdatedBy())
                    .build();
            }

            mapping = empShiftMappingRepository.save(mapping);
            return convertToEmpShiftMappingDTO(mapping);

        } catch (Exception e) {
            log.error("Error updating employee shift mapping", e);
            throw new RuntimeException("Failed to update employee shift mapping", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<CafeShiftDataDTO> getCafeShiftData(Integer unitId) {
        log.info("Getting cafe shift data for unitId: {}", unitId);

        try {
            List<CafeShiftDataDTO> cafeShiftData = new ArrayList<>();

            List<UnitBasicDetail> units;
            if (unitId != null) {
                UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(unitId);
                units = unit != null ? List.of(unit) : new ArrayList<>();
            } else {
                units = unitCacheService.getAllUnitBasicDetail();
            }

            for (UnitBasicDetail unit : units) {
                CafeShiftDataDTO cafeData = buildCafeShiftData(unit);
                cafeShiftData.add(cafeData);
            }

            return cafeShiftData;

        } catch (Exception e) {
            log.error("Error getting cafe shift data", e);
            throw new RuntimeException("Failed to get cafe shift data", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<HierarchyEmployeeDTO> getHierarchyEmployees(Integer managerId, boolean includeSubordinates) {
        log.info("Getting hierarchy employees for managerId: {}, includeSubordinates: {}", managerId, includeSubordinates);

        try {
            // This is a simplified implementation
            // In a real scenario, you would need to implement proper hierarchy traversal
            List<HierarchyEmployeeDTO> employees = new ArrayList<>();

            // Get all employees from cache
            Map<Integer, EmployeeBasicDetail> allEmployees = userCacheService.getAllUserCache();

            if (managerId != null) {
                // Get employees reporting to specific manager
                allEmployees.values().stream()
                    .filter(emp -> managerId.equals(emp.getReportingManagerId()))
                    .forEach(emp -> {
                        HierarchyEmployeeDTO empDTO = convertToHierarchyEmployeeDTO(emp, 1);
                        if (includeSubordinates) {
                            empDTO.setSubordinates(getHierarchyEmployees(emp.getId(), true));
                        }
                        employees.add(empDTO);
                    });
            } else {
                // Get top-level employees (those without reporting manager)
                allEmployees.values().stream()
                    .filter(emp -> emp.getReportingManagerId() == null)
                    .forEach(emp -> {
                        HierarchyEmployeeDTO empDTO = convertToHierarchyEmployeeDTO(emp, 0);
                        if (includeSubordinates) {
                            empDTO.setSubordinates(getHierarchyEmployees(emp.getId(), true));
                        }
                        employees.add(empDTO);
                    });
            }

            return employees;

        } catch (Exception e) {
            log.error("Error getting hierarchy employees", e);
            throw new RuntimeException("Failed to get hierarchy employees", e);
        }
    }

    // Additional utility methods implementation

    @Override
    public ShiftResponseDTO createShift(ShiftRequestDTO shiftRequestDTO, String createdBy) {
        log.info("Creating new shift: {}", shiftRequestDTO.getShiftName());
        if(shiftRequestDTO.getStartTime().isAfter(shiftRequestDTO.getEndTime())){
            throw new RuntimeException("Start time cannot be after end time");
        }
        // validate shift_name,startTime,endTime form db
        if(shiftRepository.existsByShiftNameIgnoreCaseAndStartTimeAndEndTimeAndStatus(shiftRequestDTO.getShiftName(),shiftRequestDTO.getStartTime(),shiftRequestDTO.getEndTime())){
            throw new RuntimeException("Shift with name startTime and endTime '" + shiftRequestDTO.getShiftName() + "' and startTime '" + shiftRequestDTO.getStartTime() + "' and endTime '" + shiftRequestDTO.getEndTime() + "' already exists");
        }
        // build the data and save to db
        try{
            Shift shift = Shift.builder()
                .shiftName(shiftRequestDTO.getShiftName())
                .startTime(shiftRequestDTO.getStartTime())
                .endTime(shiftRequestDTO.getEndTime())
                .status("ACTIVE")
                .createdBy(createdBy)
                .updatedBy(createdBy)
                .build();

            shift = shiftRepository.save(shift);
            return convertToShiftResponseDTO(shift);
        } catch (Exception e) {
            log.error("Error creating shift", e);
            throw new RuntimeException("Failed to create shift", e);
        }
    }

    @Override
    public ShiftResponseDTO updateShift(Integer shiftId, ShiftRequestDTO shiftRequestDTO, String updatedBy) {
        log.info("Updating shift with ID: {}", shiftId);

        try {
            com.stpl.tech.attendance.entity.RosteringEntity.Shift shift = shiftRepository.findById(shiftId)
                .orElseThrow(() -> new RuntimeException("Shift not found"));

            // Check if shift name already exists (excluding current shift)
            if (shiftRepository.existsByShiftNameIgnoreCaseAndShiftIdNotAndStatus(
                shiftRequestDTO.getShiftName(), shiftId)) {
                throw new RuntimeException("Shift with name '" + shiftRequestDTO.getShiftName() + "' already exists");
            }

            shift.setShiftName(shiftRequestDTO.getShiftName());
            shift.setStartTime(shiftRequestDTO.getStartTime());
            shift.setEndTime(shiftRequestDTO.getEndTime());
            shift.setUpdatedBy(updatedBy); // From HttpServletRequest

            shift = shiftRepository.save(shift);
            return convertToShiftResponseDTO(shift);

        } catch (Exception e) {
            log.error("Error updating shift", e);
            throw new RuntimeException("Failed to update shift", e);
        }
    }

    @Override
    public void deleteShift(Integer shiftId) {
        log.info("Deleting shift with ID: {}", shiftId);

        try {
            com.stpl.tech.attendance.entity.RosteringEntity.Shift shift = shiftRepository.findById(shiftId)
                .orElseThrow(() -> new RuntimeException("Shift not found"));

            shift.setStatus("INACTIVE");
            shiftRepository.save(shift);

        } catch (Exception e) {
            log.error("Error deleting shift", e);
            throw new RuntimeException("Failed to delete shift", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<ShiftResponseDTO> getAllShifts(String status) {
        log.info("Getting all shifts with status: {}", status);

        try {
            List<com.stpl.tech.attendance.entity.RosteringEntity.Shift> shifts = status != null ?
                shiftRepository.findByStatus(status) :
                shiftRepository.findAll();

            return shifts.stream()
                .map(this::convertToShiftResponseDTO)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error getting all shifts", e);
            throw new RuntimeException("Failed to get all shifts", e);
        }
    }

    @Override
    public ShiftCafeMappingDTO createShiftCafeMapping(Integer shiftId, Integer unitId, String createdBy) {
        log.info("Creating shift cafe mapping for shiftId: {}, unitId: {}", shiftId, unitId);

        try {
            // Check if mapping already exists
            if (shiftCafeMappingRepository.existsByShiftIdAndUnitIdAndStatus(shiftId, unitId, "ACTIVE")) {
                throw new RuntimeException("Shift cafe mapping already exists");
            }

            ShiftCafeMapping mapping = ShiftCafeMapping.builder()
                .shiftId(shiftId)
                .unitId(unitId)
                .status("ACTIVE")
                .createdBy(createdBy)
                .updatedBy(createdBy)
                .build();

            mapping = shiftCafeMappingRepository.save(mapping);
            return convertToShiftCafeMappingDTO(mapping);

        } catch (Exception e) {
            log.error("Error creating shift cafe mapping", e);
            throw new RuntimeException("Failed to create shift cafe mapping", e);
        }
    }

    @Override
    public void deleteShiftCafeMapping(Integer shiftId, Integer unitId) {
        log.info("Deleting shift cafe mapping for shiftId: {}, unitId: {}", shiftId, unitId);

        try {
            ShiftCafeMapping mapping = shiftCafeMappingRepository
                .findByShiftIdAndUnitIdAndStatus(shiftId, unitId, "ACTIVE")
                .orElseThrow(() -> new RuntimeException("Shift cafe mapping not found"));

            mapping.setStatus("INACTIVE");
            shiftCafeMappingRepository.save(mapping);

        } catch (Exception e) {
            log.error("Error deleting shift cafe mapping", e);
            throw new RuntimeException("Failed to delete shift cafe mapping", e);
        }
    }

    @Override
    public EmpShiftMappingDTO createEmpShiftMapping(EmpShiftUpdateRequestDTO request) {
        log.info("Creating employee shift mapping: {}", request);

        try {
            // Check for overlapping mappings
            List<EmpShiftMapping> overlapping = empShiftMappingRepository.findOverlappingMappings(
                request.getEmpId(), "ACTIVE", -1, request.getStartDate(), request.getEndDate());

            if (!overlapping.isEmpty()) {
                throw new RuntimeException("Employee already has overlapping shift mapping");
            }

            EmpShiftMapping mapping = EmpShiftMapping.builder()
                .empId(request.getEmpId())
                .shiftId(request.getShiftId())
                .startDate(request.getStartDate())
                .endDate(request.getEndDate())
                .expectedStartDate(request.getExpectedStartDate())
                .expectedEndDate(request.getExpectedEndDate())
                .processingFrom(request.getProcessingFrom())
                .processingTo(request.getProcessingTo())
                .businessFrom(request.getBusinessFrom())
                .businessTo(request.getBusinessTo())
                .status(request.getStatus())
                .createdBy(request.getUpdatedBy())
                .updatedBy(request.getUpdatedBy())
                .build();

            mapping = empShiftMappingRepository.save(mapping);
            return convertToEmpShiftMappingDTO(mapping);

        } catch (Exception e) {
            log.error("Error creating employee shift mapping", e);
            throw new RuntimeException("Failed to create employee shift mapping", e);
        }
    }

    @Override
    public void deleteEmpShiftMapping(Integer mappingId) {
        log.info("Deleting employee shift mapping with ID: {}", mappingId);

        try {
            EmpShiftMapping mapping = empShiftMappingRepository.findById(mappingId)
                .orElseThrow(() -> new RuntimeException("Employee shift mapping not found"));

            mapping.setStatus("INACTIVE");
            empShiftMappingRepository.save(mapping);

        } catch (Exception e) {
            log.error("Error deleting employee shift mapping", e);
            throw new RuntimeException("Failed to delete employee shift mapping", e);
        }
    }

    // Private helper methods

    private CafeLiveDashboardDTO buildCafeDashboard(UnitBasicDetail unit) {
        // Get shifts for this unit
        List<ShiftCafeMapping> shiftMappings = shiftCafeMappingRepository
            .findByUnitIdAndStatusWithShift(unit.getId(), "ACTIVE");

        List<CafeLiveDashboardDTO.ShiftSummaryDTO> shiftSummaries = shiftMappings.stream()
            .map(mapping -> {
                com.stpl.tech.attendance.entity.RosteringEntity.Shift shift = mapping.getShift();
                List<Integer> empIds = empShiftMappingRepository
                    .findEmpIdsByShiftIdAndStatus(shift.getShiftId(), "ACTIVE");

                return CafeLiveDashboardDTO.ShiftSummaryDTO.builder()
                    .shiftId(shift.getShiftId())
                    .shiftName(shift.getShiftName())
                    .shiftStartTime(shift.getStartTime())
                    .shiftEndTime(shift.getEndTime())
                    .totalEmployeesInShift(empIds.size())
                    .presentEmployeesInShift(0) // TODO: Calculate from attendance data
                    .absentEmployeesInShift(empIds.size()) // TODO: Calculate from attendance data
                    .build();
            })
            .collect(Collectors.toList());

        int totalEmployees = shiftSummaries.stream()
            .mapToInt(CafeLiveDashboardDTO.ShiftSummaryDTO::getTotalEmployeesInShift)
            .sum();

        return CafeLiveDashboardDTO.builder()
            .unitId(unit.getId())
            .unitName(unit.getName())
            .unitCode(unit.getReferenceName())
            .totalEmployees(totalEmployees)
            .presentEmployees(0) // TODO: Calculate from attendance data
            .absentEmployees(totalEmployees) // TODO: Calculate from attendance data
            .onBreakEmployees(0) // TODO: Calculate from attendance data
            .shiftSummaries(shiftSummaries)
            .lastUpdated(LocalDateTime.now())
            .build();
    }

    private ShiftEmployeesDTO buildShiftEmployees(com.stpl.tech.attendance.entity.RosteringEntity.Shift shift) {
        List<Integer> empIds = empShiftMappingRepository
            .findEmpIdsByShiftIdAndStatus(shift.getShiftId(), "ACTIVE");

        List<ShiftEmployeesDTO.EmployeeShiftDetailDTO> employees = empIds.stream()
            .map(empId -> {
                EmployeeBasicDetail emp = userCacheService.getUserById(empId);
                if (emp != null) {
                    return ShiftEmployeesDTO.EmployeeShiftDetailDTO.builder()
                        .empId(emp.getId())
                        .empName(emp.getName())
                        .empCode(emp.getEmployeeCode())
                        .designation(emp.getDesignation())
                        .contactNumber(emp.getContactNumber())
                        .status(emp.getStatus().name())
                        .attendanceStatus("UNKNOWN") // TODO: Calculate from attendance data
                        .build();
                }
                return null;
            })
            .filter(emp -> emp != null)
            .collect(Collectors.toList());

        return ShiftEmployeesDTO.builder()
            .shiftId(shift.getShiftId())
            .shiftName(shift.getShiftName())
            .shiftStartTime(shift.getStartTime())
            .shiftEndTime(shift.getEndTime())
            .totalEmployees(employees.size())
            .employees(employees)
            .build();
    }

    private CafeShiftDataDTO buildCafeShiftData(UnitBasicDetail unit) {
        List<ShiftCafeMapping> shiftMappings = shiftCafeMappingRepository
            .findByUnitIdAndStatusWithShift(unit.getId(), "ACTIVE");

        List<CafeShiftDataDTO.ShiftDetailDTO> shifts = shiftMappings.stream()
            .map(mapping -> {
                com.stpl.tech.attendance.entity.RosteringEntity.Shift shift = mapping.getShift();
                List<Integer> empIds = empShiftMappingRepository
                    .findEmpIdsByShiftIdAndStatus(shift.getShiftId(), "ACTIVE");

                return CafeShiftDataDTO.ShiftDetailDTO.builder()
                    .shiftId(shift.getShiftId())
                    .shiftName(shift.getShiftName())
                    .startTime(shift.getStartTime())
                    .endTime(shift.getEndTime())
                    .status(shift.getStatus())
                    .totalEmployees(empIds.size())
                    .creationTime(shift.getCreationTime())
                    .createdBy(shift.getCreatedBy())
                    .build();
            })
            .collect(Collectors.toList());

        return CafeShiftDataDTO.builder()
            .unitId(unit.getId())
            .unitName(unit.getName())
            .unitCode(unit.getReferenceName())
            .shifts(shifts)
            .build();
    }

    private Shift convertToShiftDTO(com.stpl.tech.attendance.entity.RosteringEntity.Shift shift) {
        return Shift.builder()
            .shiftId(shift.getShiftId())
            .shiftName(shift.getShiftName())
            .startTime(shift.getStartTime())
            .endTime(shift.getEndTime())
            .status(shift.getStatus())
            .createdBy(shift.getCreatedBy())
            .creationTime(shift.getCreationTime())
            .updatedBy(shift.getUpdatedBy())
            .updationTime(shift.getUpdationTime())
            .build();
    }

    private ShiftResponseDTO convertToShiftResponseDTO(com.stpl.tech.attendance.entity.RosteringEntity.Shift shift) {
        return ShiftResponseDTO.builder()
            .shiftId(shift.getShiftId()) // Auto-generated from DB
            .shiftName(shift.getShiftName())
            .startTime(shift.getStartTime())
            .endTime(shift.getEndTime())
            .status(shift.getStatus())
            .createdBy(shift.getCreatedBy()) // Set from HttpServletRequest
            .creationTime(shift.getCreationTime()) // Set from current system time
            .updatedBy(shift.getUpdatedBy()) // Set from HttpServletRequest
            .updationTime(shift.getUpdationTime()) // Set from current system time
            .build();
    }

    private ShiftCafeMappingDTO convertToShiftCafeMappingDTO(ShiftCafeMapping mapping) {
        String shiftName = null;
        String unitName = null;

        if (mapping.getShift() != null) {
            shiftName = mapping.getShift().getShiftName();
        }

        UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(mapping.getUnitId());
        if (unit != null) {
            unitName = unit.getName();
        }

        return ShiftCafeMappingDTO.builder()
            .shiftCafeMappingId(mapping.getShiftCafeMappingId())
            .shiftId(mapping.getShiftId())
            .shiftName(shiftName)
            .unitId(mapping.getUnitId())
            .unitName(unitName)
            .status(mapping.getStatus())
            .createdBy(mapping.getCreatedBy())
            .creationTime(mapping.getCreationTime())
            .updatedBy(mapping.getUpdatedBy())
            .updationTime(mapping.getUpdationTime())
            .build();
    }

    private EmpShiftMappingDTO convertToEmpShiftMappingDTO(EmpShiftMapping mapping) {
        String shiftName = null;
        String empName = null;
        String empCode = null;

        if (mapping.getShift() != null) {
            shiftName = mapping.getShift().getShiftName();
        }

        EmployeeBasicDetail emp = userCacheService.getUserById(mapping.getEmpId());
        if (emp != null) {
            empName = emp.getName();
            empCode = emp.getEmployeeCode();
        }

        return EmpShiftMappingDTO.builder()
            .id(mapping.getId())
            .shiftId(mapping.getShiftId())
            .shiftName(shiftName)
            .empId(mapping.getEmpId())
            .empName(empName)
            .empCode(empCode)
            .startDate(mapping.getStartDate())
            .endDate(mapping.getEndDate())
            .expectedStartDate(mapping.getExpectedStartDate())
            .expectedEndDate(mapping.getExpectedEndDate())
            .processingFrom(mapping.getProcessingFrom())
            .processingTo(mapping.getProcessingTo())
            .businessFrom(mapping.getBusinessFrom())
            .businessTo(mapping.getBusinessTo())
            .status(mapping.getStatus())
            .createdBy(mapping.getCreatedBy())
            .creationTime(mapping.getCreationTime())
            .updatedBy(mapping.getUpdatedBy())
            .updationTime(mapping.getUpdationTime())
            .build();
    }

    private HierarchyEmployeeDTO convertToHierarchyEmployeeDTO(EmployeeBasicDetail emp, int level) {
        String reportingManagerName = null;
        if (emp.getReportingManagerId() != null) {
            EmployeeBasicDetail manager = userCacheService.getUserById(emp.getReportingManagerId());
            if (manager != null) {
                reportingManagerName = manager.getName();
            }
        }

        return HierarchyEmployeeDTO.builder()
            .empId(emp.getId())
            .empName(emp.getName())
            .empCode(emp.getEmployeeCode())
            .designation(emp.getDesignation())
            .contactNumber(emp.getContactNumber())
            .emailId(emp.getEmailId())
            .status(emp.getStatus().name())
            .reportingManagerId(emp.getReportingManagerId())
            .reportingManagerName(reportingManagerName)
            .departmentId(emp.getDepartmentId())
            .departmentName(emp.getDepartmentName())
            .level(level)
            .subordinates(new ArrayList<>())
            .build();
    }
}
