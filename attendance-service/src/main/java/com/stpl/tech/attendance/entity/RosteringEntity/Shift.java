package com.stpl.tech.attendance.entity.RosteringEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

@Entity
@Table(name = "SHIFTS")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Shift {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SHIFT_ID")
    private Integer shiftId;
    
    @Column(name = "SHIFT_NAME", length = 100, nullable = false)
    private String shiftName;
    
    @Column(name = "START_TIME", nullable = false)
    private LocalDateTime startTime;
    
    @Column(name = "END_TIME", nullable = false)
    private LocalDateTime endTime;
    
    @Column(name = "STATUS", length = 45, nullable = false)
    private String status = "ACTIVE";
    
    @Column(name = "CREATED_BY", length = 100)
    private String createdBy;
    
    @CreationTimestamp
    @Column(name = "CREATION_TIME", nullable = false, updatable = false)
    private LocalDateTime creationTime;
    
    @Column(name = "UPDATED_BY", length = 100)
    private String updatedBy;
    
    @UpdateTimestamp
    @Column(name = "UPDATION_TIME", nullable = false)
    private LocalDateTime updationTime;
}
