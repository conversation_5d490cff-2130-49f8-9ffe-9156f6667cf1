package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CafeLiveDashboardDTO {
    private Integer unitId;
    private String unitName;
    private String unitCode;
    private Integer totalEmployees;
    private Integer presentEmployees;
    private Integer absentEmployees;
    private Integer onBreakEmployees;
    private List<ShiftSummaryDTO> shiftSummaries;
    private LocalDateTime lastUpdated;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShiftSummaryDTO {
        private Integer shiftId;
        private String shiftName;
        private LocalDateTime shiftStartTime;
        private LocalDateTime shiftEndTime;
        private Integer totalEmployeesInShift;
        private Integer presentEmployeesInShift;
        private Integer absentEmployeesInShift;
    }
}
