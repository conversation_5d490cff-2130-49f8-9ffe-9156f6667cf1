package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Shift {
    private Integer shiftId;
    private String shiftName;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private String status;
    private String createdBy;
    private LocalDateTime creationTime;
    private String updatedBy;
    private LocalDateTime updationTime;
}
