package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShiftCafeMappingDTO {
    private Integer shiftCafeMappingId;
    private Integer shiftId;
    private String shiftName;
    private Integer unitId;
    private String unitName;
    private String status;
    private String createdBy;
    private LocalDateTime creationTime;
    private String updatedBy;
    private LocalDateTime updationTime;
}
