package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HierarchyEmployeeDTO {
    private Integer empId;
    private String empName;
    private String empCode;
    private String designation;
    private String contactNumber;
    private String emailId;
    private String status;
    private Integer reportingManagerId;
    private String reportingManagerName;
    private Integer departmentId;
    private String departmentName;
    private List<HierarchyEmployeeDTO> subordinates; // Direct reports
    private Integer level; // Hierarchy level (0 for top level)
}
