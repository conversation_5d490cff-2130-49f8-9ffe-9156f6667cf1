package com.stpl.tech.attendance.interceptor;

import com.stpl.tech.attendance.cache.service.SessionCache;
import com.stpl.tech.attendance.context.JwtContext;
import com.stpl.tech.attendance.exception.AuthenticationFailureException;
import com.stpl.tech.attendance.model.JWTToken;
import com.stpl.tech.attendance.service.LocationValidationService;
import com.stpl.tech.attendance.service.TokenService;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.security.SignatureException;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthInterceptor implements HandlerInterceptor {

    private final TokenService<JWTToken> jwtService;

    @Autowired
    private Environment env;

    @Autowired
    private LocationValidationService locationValidationService;

    @Autowired
    private SessionCache sessionCache;

    @Autowired
    private Environment environment;

    @Autowired
    private Environment serverEnvironment;

    @Autowired
    private Environment serverServletEnvironment;

    private static boolean forTesting = true;

    // List of paths that don't require authentication
    private static final List<String> PUBLIC_PATHS = Arrays.asList(
        "/user/authenticate",
        "/user/refresh-token",
        "/health",
        "/swagger-ui",
        "/swagger-ui.html",
        "/swagger-ui/index.html",
        "/swagger-ui/**",
        "/api-docs",
        "/api-docs/**",
        "/v3/api-docs",
        "/v3/api-docs/**",
        "/swagger-resources",
        "/swagger-resources/**",
        "/webjars/**",
        "/actuator",
        "/actuator/health",
        "/error",
            "validate-pairing"
    );

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) 
            throws AuthenticationFailureException {
        if(forTesting) {
            return  true;
        }
        // Set request start time for timing calculation
        request.setAttribute("startTime", System.currentTimeMillis());
        
        String requestPath = request.getRequestURI();
        log.debug("Processing request path: {}", requestPath);

        // Get context path from environment
        String contextPath = serverServletEnvironment.getProperty("server.servlet.context-path", "/attendance-service");
        Boolean testMode = Boolean.valueOf(serverServletEnvironment.getProperty("test.mode", "true"));
        /*if(Boolean.TRUE.equals(testMode)){
            return true;
        }*/
        log.debug("Context path: {}", contextPath);
        
        // Remove context path from request path for comparison
        String pathWithoutContext = requestPath;
        if (requestPath.startsWith(contextPath)) {
            pathWithoutContext = requestPath.substring(contextPath.length());
            // Ensure path starts with a slash
            if (!pathWithoutContext.startsWith("/")) {
                pathWithoutContext = "/" + pathWithoutContext;
            }
        }
        log.debug("Path without context: {}", pathWithoutContext);

        // Skip authentication for public paths
        if (isPublicPath(pathWithoutContext)) {
            log.debug("Public path accessed: {}", pathWithoutContext);
            return true;
        }

        String token = extractToken(request);
        if (token == null) {
            log.error("No JWT token found in request");
            throw new AuthenticationFailureException("No authentication token provided");
        }

        try {
            // Parse and validate the JWT token
            JWTToken jwtToken = new JWTToken();
            jwtService.parseToken(jwtToken, token);

            // Validate session
            validateSession(jwtToken);

            // Handle null checks for userId and unitId
            Integer userId = jwtToken.getUserId();
            Integer unitId = jwtToken.getUnitId();
            Integer terminalId = jwtToken.getTerminalId();
            String sessionKey = jwtToken.getSessionKey();

            // Set JWT context
            JwtContext.setContext(userId, unitId, terminalId);

            // Set user context in MDC for logging
            RequestIdInterceptor.setUserContext(userId, unitId, sessionKey);

            // Set device context from headers
            String deviceId = request.getHeader("X-MAC-Address");
            String latLong = request.getHeader("X-Geo-Location");
            if (deviceId != null || latLong != null) {
                JwtContext.setDeviceContext(deviceId, latLong);
            }


            // Add user context to request attributes for use in controllers
            request.setAttribute("userId", userId);
            request.setAttribute("unitId", unitId);

            // Validate location if enabled
            validateLocationIfEnabled(request, unitId);

            return true;

        } catch (ExpiredJwtException e) {
            log.warn("JWT token has expired: {}", e.getMessage());
            throw new AuthenticationFailureException("Token has expired");
        } catch (MalformedJwtException e) {
            log.warn("Malformed JWT token: {}", e.getMessage());
            throw new AuthenticationFailureException("Invalid token format");
        } catch (Exception e) {
            log.error("Error validating JWT token", e);
            throw new AuthenticationFailureException("Token validation failed");
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // Clear the JWT context after request completion
        JwtContext.clear();
    }

    private void validateSession(JWTToken jwtToken) throws AuthenticationFailureException {
        String sessionKey = jwtToken.getSessionKey();
        Integer userId = jwtToken.getUserId();
        Integer unitId = jwtToken.getUnitId();

        if (sessionKey == null || userId == null || unitId == null) {
            log.warn("Invalid session data in token: sessionKey={}, userId={}, unitId={}", 
                sessionKey, userId, unitId);
            throw new AuthenticationFailureException("Invalid session data in token");
        }

        try {
            // Validate session using SessionCache
            if (!sessionCache.validateSession(sessionKey,unitId, userId)) {
                log.warn("Invalid session: unitId={}, userId={}, sessionKey={}", 
                    unitId, userId, sessionKey);
                throw new AuthenticationFailureException("Invalid or expired session");
            }
        } catch (Exception e) {
            log.error("Error validating session", e);
            throw new AuthenticationFailureException("Session validation failed");
        }
    }

    private void validateLocationIfEnabled(HttpServletRequest request, Integer unitId) 
            throws AuthenticationFailureException {
        
        // Check if location validation is enabled
        boolean validateLocation = Boolean.parseBoolean(
            env.getProperty("location.validation.enabled", "true"));
        
        if (!validateLocation) {
            return;
        }

        String applicationId = request.getHeader("X-Application-Id");
        String macAddress = request.getHeader("X-MAC-Address");
        String geoLocation = request.getHeader("X-Geo-Location");

        // Validate location if headers are provided
        if (applicationId != null && (macAddress != null || geoLocation != null)) {
            try {
                locationValidationService.validateLocation(Integer.valueOf(applicationId), macAddress, geoLocation, unitId);
            } catch (Exception e) {
                log.error("Location validation failed for unitId: {}, error: {}", unitId, e.getMessage());
                throw new AuthenticationFailureException("Location validation failed");
            }
        }
    }

    private String extractToken(HttpServletRequest request) {
        String bearerToken = request.getHeader("Auth");
        if (bearerToken != null) {
            return bearerToken;
        }
        return null;
    }

    private boolean isPublicPath(String requestPath) {
        // Ensure path starts with a slash
        String normalizedPath = requestPath.startsWith("/") ? requestPath : "/" + requestPath;
        log.info("Checking if path is public: {}", normalizedPath);
        
        return PUBLIC_PATHS.stream()
                .anyMatch(pattern -> {
                    if (pattern.endsWith("/**")) {
                        String prefix = pattern.substring(0, pattern.length() - 3);
                        boolean matches = normalizedPath.startsWith(prefix);
                        if (matches) {
                            log.debug("Path {} matches pattern {}", normalizedPath, pattern);
                        }
                        return matches;
                    }
                    boolean matches = normalizedPath.contains(pattern);
                    if (matches) {
                        log.debug("Path {} matches pattern {}", normalizedPath, pattern);
                    }
                    return matches;
                });
    }
} 