package com.stpl.tech.attendance.constants;

public final class ApiConstants {
    private ApiConstants() {
        // Private constructor to prevent instantiation
    }

    // Base API path without version
    public static final String API_BASE = "/api";

    // Version constants for different controllers
    public static final class Versions {
        private Versions() {}
        
        public static final String V1 = "v1";
        public static final String V2 = "v2";
        public static final String V3 = "v3";
    }

    // API paths for different controllers
    public static final class Paths {

        private Paths() {}

        public static final String ATTENDANCE = API_BASE + "/" + Versions.V1 + "/attendance";
        public static final String APPROVALS = API_BASE + "/" + Versions.V1 + "/approvals";
        public static final String BIOMETRIC = API_BASE + "/" + Versions.V1 + "/biometric";
        public static final String METADATA = API_BASE + "/" + Versions.V1 + "/metadata";
        public static final String NOTIFICATIONS = API_BASE + "/" + Versions.V1 + "/notifications"; ;
        public static final String TRANSFER = API_BASE + "/" + Versions.V1 + "/transfers"; ;
        public static final String ROSTER = API_BASE + "/" + Versions.V1 + "/roster";
    }

    // Helper method to build versioned path
    public static String buildPath(String version, String resource) {
        return API_BASE + "/" + version + "/" + resource;
    }
} 