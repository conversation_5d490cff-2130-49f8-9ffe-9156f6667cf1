package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShiftEmployeesDTO {
    private Integer shiftId;
    private String shiftName;
    private LocalDateTime shiftStartTime;
    private LocalDateTime shiftEndTime;
    private Integer totalEmployees;
    private List<EmployeeShiftDetailDTO> employees;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EmployeeShiftDetailDTO {
        private Integer empId;
        private String empName;
        private String empCode;
        private String designation;
        private String contactNumber;
        private String status; // ACTIVE, INACTIVE
        private LocalDateTime shiftStartDate;
        private LocalDateTime shiftEndDate;
        private String attendanceStatus; // PRESENT, ABSENT, ON_BREAK
        private LocalDateTime lastPunchTime;
    }
}
