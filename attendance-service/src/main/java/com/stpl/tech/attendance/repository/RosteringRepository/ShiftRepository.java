package com.stpl.tech.attendance.repository.RosteringRepository;

import com.stpl.tech.attendance.entity.RosteringEntity.Shift;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ShiftRepository extends JpaRepository<Shift, Integer> {
    
    /**
     * Find all active shifts
     */
    List<Shift> findByStatus(String status);
    
    /**
     * Find shift by name
     */
    Optional<Shift> findByShiftNameAndStatus(String shiftName, String status);
    
    /**
     * Find shifts by name containing (case insensitive)
     */
    @Query("SELECT s FROM Shift s WHERE LOWER(s.shiftName) LIKE LOWER(CONCAT('%', :name, '%')) AND s.status = :status")
    List<Shift> findByShiftNameContainingIgnoreCaseAndStatus(@Param("name") String name, @Param("status") String status);
    
    /**
     * Check if shift name exists (excluding specific shift ID)
     */
    @Query("SELECT COUNT(s) > 0 FROM Shift s WHERE LOWER(s.shiftName) = LOWER(:shiftName) AND s.shiftId != :shiftId AND s.status = 'ACTIVE'")
    boolean existsByShiftNameIgnoreCaseAndShiftIdNotAndStatus(@Param("shiftName") String shiftName, @Param("shiftId") Integer shiftId);
    
    /**
     * Check if shift name exists
     */
    boolean existsByShiftId(Integer shiftId);

    // check if shiftName,startTime,endTime exist in db
    @Query("SELECT COUNT(s) > 0 FROM Shift s WHERE LOWER(s.shiftName) = LOWER(:shiftName) AND s.startTime = :startTime AND s.endTime = :endTime AND s.status = 'ACTIVE'")
    boolean existsByShiftNameIgnoreCaseAndStartTimeAndEndTimeAndStatus(@Param("shiftName") String shiftName, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
