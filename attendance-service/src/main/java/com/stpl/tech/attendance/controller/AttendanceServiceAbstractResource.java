package com.stpl.tech.attendance.controller;

import com.stpl.tech.attendance.enums.AttendanceErrorCode;
import com.stpl.tech.attendance.exception.AttendanceException;
import com.stpl.tech.attendance.model.JWTToken;
import com.stpl.tech.attendance.service.TokenService;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;



public class AttendanceServiceAbstractResource {
    

        @Autowired
        private TokenService<JWTToken> jwtService;

        private static final Logger LOG = LoggerFactory.getLogger(AttendanceServiceAbstractResource.class);

        @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
        @ExceptionHandler(AttendanceException.class)
        @ResponseBody
        public AttendanceErrorCode handleException(HttpServletRequest req, AttendanceException ex) {
            LOG.error(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
            return ex.getErrorCode();
        }

        public Integer getLoggedInUser(HttpServletRequest request) {
            try {
                String authHeader = request.getHeader("auth") != null ? request.getHeader("auth").trim() : null;
                if (authHeader != null && !authHeader.equals("null") && !authHeader.equals("")) {
                    JWTToken jwtToken = new JWTToken();
                    jwtService.parseToken(jwtToken, authHeader);
                    return Integer.valueOf(jwtToken.getUserId());
                }
            } catch (Exception e) {
            }
            return -1;
        }
}
